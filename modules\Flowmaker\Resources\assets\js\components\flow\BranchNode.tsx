import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Position, useReactFlow } from '@xyflow/react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { GitFork, Plus, X } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { BranchCondition, WebhookVariable, NodeData } from '@/types/flow';
import { useFlowVariables } from '@/hooks/useFlowVariables';
import { useFlowActions } from "@/hooks/useFlowActions";

interface BranchNodeProps {
  id: string;
  data: NodeData;
}

const operators = [
  { value: 'equals', label: 'Equals' },
  { value: 'not_equals', label: 'Not Equals' },
  { value: 'greater_than', label: 'Greater Than' },
  { value: 'less_than', label: 'Less Than' },
  { value: 'contains', label: 'Contains' },
  { value: 'not_contains', label: 'Not Contains' },
] as const;

const BranchNode = ({ id, data }: BranchNodeProps) => {
  const { getNodes, setNodes } = useReactFlow();
  const { groupedVariables } = useFlowVariables();

  // Initialize conditions from data
  const [conditions, setConditions] = useState<BranchCondition[]>(() => {
    const originalConditions = data.settings?.conditions || [];
    console.log(`BranchNode ${id}: Initializing with conditions:`, originalConditions);

    // Ensure each condition has a unique ID
    return originalConditions.map((condition: any, index: number) => ({
      ...condition,
      id: condition.id || `condition-${Date.now()}-${index}`
    }));
  });

  // DISABLED: Sync local state with data changes - THIS IS CAUSING THE ISSUE
  // useEffect(() => {
  //   const dataConditions = data.settings?.conditions || [];
  //   const dataConditionsStr = JSON.stringify(dataConditions);
  //   const localConditionsStr = JSON.stringify(conditions);

  //   console.log('🔄 useEffect sync check for node', id, {
  //     dataConditions,
  //     localConditions: conditions,
  //     dataConditionsStr,
  //     localConditionsStr,
  //     areEqual: dataConditionsStr === localConditionsStr
  //   });

  //   if (dataConditionsStr !== localConditionsStr) {
  //     console.log('� Syncing conditions for node', id, 'from:', conditions, 'to:', dataConditions);
  //     // Create completely new condition objects with new IDs to prevent any reference sharing
  //     const newConditions = dataConditions.map(condition => ({
  //       ...condition,
  //       id: `${Date.now()}-${Math.random().toString(36).substring(2, 15)}-${id}` // Include node ID for uniqueness
  //     }));
  //     setConditions(newConditions);
  //   }
  // }, [data.settings?.conditions, id]);

  // Update the global state whenever local conditions change
  useEffect(() => {
    setNodes((nodes) => {
      return nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              settings: {
                ...node.data.settings,
                conditions: [...conditions], // Create a new array reference
              },
            }
          };
        }
        return node;
      });
    });
  }, [conditions, id, setNodes]);

  const webhookNode = getNodes().find((node) => node.type === 'webhook');
  const webhookVariables = (webhookNode?.data as NodeData)?.settings?.webhook?.variables || [];



  const addCondition = () => {
    const uniqueId = `condition-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
    const newCondition: BranchCondition = {
      id: uniqueId,
      variableId: '',
      operator: 'equals',
      value: '',
    };
    console.log(`BranchNode ${id}: Adding condition:`, newCondition);
    setConditions([...conditions, newCondition]);
  };

  const removeCondition = (conditionId: string) => {
    console.log(`BranchNode ${id}: Removing condition:`, conditionId);
    setConditions(conditions.filter((c) => c.id !== conditionId));
  };

  return (
    <div className="bg-white rounded-lg shadow-lg">
      <Handle 
        type="target" 
        position={Position.Left}
        className="!bg-gray-300 !w-3 !h-3 !rounded-full"
      />
      
      <div className="flex items-center gap-2 mb-4 pb-2 border-b border-gray-100 px-4 pt-3 bg-gray-50">
        <GitFork className="h-4 w-4 text-purple-600" />
        <div className="font-medium">Branch</div>
      </div>

      <div className="p-4">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium">Branch</h3>
            <Button variant="outline" size="sm" onClick={addCondition}>
              <Plus className="h-4 w-4 mr-1" />
              Add Condition
            </Button>
          </div>

          <div className="space-y-3">
            {conditions.map((condition) => (
              <div key={condition.id} className="flex items-center gap-2">
                <Select
                  value={condition.variableId}
                  onValueChange={(value) => {
                    const updatedConditions = conditions.map((c) =>
                      c.id === condition.id ? { ...c, variableId: value } : c
                    );
                    setConditions(updatedConditions);
                  }}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Variable" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(groupedVariables).map(([category, categoryVariables]) => (
                      <div key={category}>
                        <div className="px-2 py-1.5 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                          {category}
                        </div>
                        {categoryVariables.map((variable) => (
                          <SelectItem key={variable.value} value={variable.value}>
                            {variable.label}
                          </SelectItem>
                        ))}
                      </div>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={condition.operator}
                  onValueChange={(value: BranchCondition['operator']) => {
                    const updatedConditions = conditions.map((c) =>
                      c.id === condition.id ? { ...c, operator: value } : c
                    );
                    setConditions(updatedConditions);
                  }}
                >
                  <SelectTrigger className="w-[120px]">
                    <SelectValue placeholder="Operator" />
                  </SelectTrigger>
                  <SelectContent>
                    {operators.map((op) => (
                      <SelectItem key={op.value} value={op.value}>
                        {op.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Input
                  placeholder="Value"
                  value={condition.value}
                  onChange={(e) => {
                    const updatedConditions = conditions.map((c) =>
                      c.id === condition.id ? { ...c, value: e.target.value } : c
                    );
                    setConditions(updatedConditions);
                  }}
                  className="flex-1"
                />

                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => removeCondition(condition.id)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Branch output handles - single true/false result for all conditions */}
      <div className="absolute right-0 top-1/2 transform -translate-y-1/2 flex flex-col gap-4">
        <Handle
          type="source"
          position={Position.Right}
          id="true"
          className="!bg-green-500 !w-3 !h-3 !rounded-full"
          style={{ top: '-10px' }}
        />
        <Handle
          type="source"
          position={Position.Right}
          id="false"
          className="!bg-red-500 !w-3 !h-3 !rounded-full"
          style={{ top: '10px' }}
        />
      </div>
    </div>
  );
};

export default BranchNode;